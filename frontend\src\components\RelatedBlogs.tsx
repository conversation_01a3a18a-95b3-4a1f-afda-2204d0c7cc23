import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Eye, Heart, Calendar, Clock } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getRandomBlogsByAuthor, getPublishedBlogs } from '@/services/blogService';
import { Blog, Author } from '@/types';
import { formatDate, cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface RelatedBlogsProps {
  authorId: string;
  authorName: string;
  authorUsername: string;
  currentBlogId: string;
  limit?: number;
}

// Backend URL for static assets
const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

const RelatedBlogs: React.FC<RelatedBlogsProps> = ({
  authorId,
  authorName,
  authorUsername,
  currentBlogId,
  limit = 3
}) => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to get complete image URL
  const getImageUrl = (featuredImage?: any) => {
    if (!featuredImage) {
      return '/images/default-blog-cover.jpg';
    }

    // Handle different URL structures
    let imageUrl = '';

    if (featuredImage.url) {
      imageUrl = featuredImage.url;
    } else if (featuredImage.path) {
      imageUrl = featuredImage.path;
    } else if (typeof featuredImage === 'string') {
      imageUrl = featuredImage;
    }

    if (!imageUrl) {
      return '/images/default-blog-cover.jpg';
    }

    // Handle absolute URLs
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Handle relative URLs
    let finalUrl = '';
    if (imageUrl.startsWith('/')) {
      finalUrl = `${BACKEND_URL.replace(/\/$/, '')}${imageUrl}`;
    } else {
      finalUrl = `${BACKEND_URL.replace(/\/$/, '')}/${imageUrl}`;
    }

    return finalUrl;
  };

  // Handle image load error with fallback
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    // Try different fallback options
    if (target.src.includes('default-blog-cover.jpg') || target.src.includes('data:image/svg+xml')) {
      // If default image also fails, use a simple placeholder
      target.src = 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="400" height="225" viewBox="0 0 400 225" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="400" height="225" fill="#F3F4F6"/>
          <rect x="175" y="90" width="50" height="45" fill="#D1D5DB"/>
          <text x="200" y="140" font-family="Arial, sans-serif" font-size="12" fill="#9CA3AF" text-anchor="middle">Blog Post</text>
        </svg>
      `);
    } else {
      target.src = '/images/default-blog-cover.jpg';
    }
  };

  useEffect(() => {
    const fetchRelatedBlogs = async () => {
      try {
        setIsLoading(true);
        setError(null);

        let response;

        // Handle fallback case - show latest published blogs
        if (authorId === 'fallback') {
          response = await getPublishedBlogs(1, limit);
          if (response.success) {
            setBlogs(response.data);
          } else {
            throw new Error('Failed to fetch latest blogs');
          }
        } else {
          // Get related blogs by author
          response = await getRandomBlogsByAuthor(authorId, currentBlogId, limit);
          if (response.success) {
            setBlogs(response.data);
          } else {
            throw new Error('Failed to fetch related blogs');
          }
        }
      } catch (err) {
        console.error('Error fetching related blogs:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    if (authorId && currentBlogId) {
      fetchRelatedBlogs();
    }
  }, [authorId, currentBlogId, limit]);

  const getCategoryColor = (categoryName: string): string => {
    const colors = {
      'Entrepreneurship': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      'Fundraising': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      'AI & Technology': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      'Product Development': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
      'Marketing': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      'Default': 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
    };
    return colors[categoryName as keyof typeof colors] || colors.Default;
  };

  if (isLoading) {
    return (
      <div className="mt-12">
        <h3 className="text-xl font-bold mb-6 text-foreground">
          {authorId === 'fallback' ? 'Latest Blog Posts' : `More from ${authorName}`}
        </h3>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(limit)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-muted rounded-xl h-56 mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return null; // Don't show the section if there's an error
  }

  if (blogs.length === 0) {
    return null; // Don't show the section if no blogs
  }

  const shouldShowAuthorLink = authorId !== 'fallback' && authorUsername !== 'all';

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 1.0 }}
      className="mt-12"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-foreground">
          {authorId === 'fallback'
            ? 'Latest Blog Posts'
            : blogs.some(blog => typeof blog.author === 'object' && blog.author._id === authorId)
              ? `More from ${authorName}`
              : 'Related Blog Posts'
          }
        </h3>
        {shouldShowAuthorLink && (
          <Link to={`/author/@${authorUsername}`}>
            <Button variant="outline" size="sm" className="hover:bg-muted">
              View all articles
            </Button>
          </Link>
        )}
        {authorId === 'fallback' && (
          <Link to="/blog">
            <Button variant="outline" size="sm" className="hover:bg-muted">
              View all blogs
            </Button>
          </Link>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {blogs.map((blog, index) => (
          <motion.div
            key={blog._id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1.0 + (index * 0.1) }}
            className="group"
          >
            <Link to={`/blog/${blog.slug || blog._id}`} className="block">
              <div className="bg-card rounded-xl shadow-sm border border-border overflow-hidden transition-all duration-300 hover:shadow-md hover:-translate-y-1 h-full flex flex-col">
                {/* Featured Image */}
                <div className="w-full h-40 relative overflow-hidden flex-shrink-0">
                  <img
                    src={getImageUrl(blog.featuredImage)}
                    alt={blog.title}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    onError={handleImageError}
                  />
                  {/* Category Badge */}
                  {typeof blog.category === 'object' && blog.category.name && (
                    <div className="absolute top-3 left-3">
                      <Badge className={getCategoryColor(blog.category.name)}>
                        {blog.category.name}
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-4 flex flex-col flex-grow">
                  <h4 className="font-semibold text-foreground group-hover:text-brand-600 transition-colors line-clamp-2 mb-2 h-12 overflow-hidden">
                    {blog.title}
                  </h4>

                  <p className="text-sm text-muted-foreground line-clamp-2 mb-3 h-10 overflow-hidden flex-grow">
                    {blog.excerpt}
                  </p>

                  {/* Meta Information */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground mt-auto">
                    <div className="flex items-center space-x-3">
                      <span className="flex items-center">
                        <Eye size={12} className="mr-1" />
                        {blog.views.toLocaleString()}
                      </span>
                      <span className="flex items-center">
                        <Heart size={12} className="mr-1" />
                        {blog.likes.toLocaleString()}
                      </span>
                      <span className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        {blog.readTime} min
                      </span>
                    </div>
                    <span className="flex items-center">
                      <Calendar size={12} className="mr-1" />
                      {formatDate(blog.publishDate || blog.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default RelatedBlogs; 