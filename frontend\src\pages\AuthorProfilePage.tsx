import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Eye, MessageSquare, Heart, Twitter, Linkedin, Globe, Mail, MapPin, Calendar, ExternalLink, DollarSign, Award, Rocket, BookOpen, FileText, PenTool, Clock, User, Share2, UserX, Home, Search, Bookmark, Crown, HandCoins  } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Author, Story, Blog, CaseStudy } from '@/types';
import { getAuthorByUsername, getAuthorStories, getAuthorBlogs, getAuthorCaseStudies, getStartupProfileByAuthor } from '@/services/userService';
import { toggleStoryLike } from '@/services/storyService';
import { toggleBlogLike } from '@/services/blogService';
import { toast } from "@/components/ui/use-toast";
import { StartupProfile } from '@/services/startupProfileService';
import { motion } from 'framer-motion';
import { useAuth } from '@/lib/AuthContext';
import caseStudyService from '@/services/caseStudyService';
import UserLogin from '@/components/UserLogin';
import SEO from "@/components/SEO";
import { useSEO } from '@/hooks/useSEO';
import favoriteService from '@/services/favoriteService';

const AuthorProfilePage = () => {
  const { username, authorId } = useParams<{ username?: string; authorId?: string }>();
  const [author, setAuthor] = useState<Author | null>(null);
  const [startupProfile, setStartupProfile] = useState<StartupProfile | null>(null);
  const [stories, setStories] = useState<Story[]>([]);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [caseStudies, setCaseStudies] = useState<CaseStudy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isStoriesLoading, setIsStoriesLoading] = useState(false);
  const [isBlogsLoading, setIsBlogsLoading] = useState(false);
  const [isCaseStudiesLoading, setIsCaseStudiesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storiesPage, setStoriesPage] = useState(1);
  const [blogsPage, setBlogsPage] = useState(1);
  const [caseStudiesPage, setCaseStudiesPage] = useState(1);
  const [hasMoreStories, setHasMoreStories] = useState(true);
  const [hasMoreBlogs, setHasMoreBlogs] = useState(true);
  const [hasMoreCaseStudies, setHasMoreCaseStudies] = useState(true);
  const [activeTab, setActiveTab] = useState("stories");
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [favoritedStories, setFavoritedStories] = useState<Set<string>>(new Set());
  const [favoritedBlogs, setFavoritedBlogs] = useState<Set<string>>(new Set());
  const [favoritedCaseStudies, setFavoritedCaseStudies] = useState<Set<string>>(new Set());

  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const { generateSEO } = useSEO();

  const userIdentifier = username || authorId;
  const cleanUsername = userIdentifier ? userIdentifier.replace(/^@/, '') : '';

  // Backend URL for static assets like avatars
  const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

  const getImageUrl = (path: string) => {
    if (!path) return '/images/default-avatar.png';
    if (path.startsWith('http')) return path;
    if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
    return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
  };

  const getFeaturedImageUrl = (featuredImage: any) => {
    if (!featuredImage) return '/images/default-story-cover.jpg';

    if (featuredImage.path) {
      return getImageUrl(featuredImage.path);
    }

    if (typeof featuredImage === 'string') {
      return getImageUrl(featuredImage);
    }

    return '/images/default-story-cover.jpg';
  };

  // Generate SEO data when author is loaded
  const authorSEO = author ? generateSEO.author({
    _id: author._id || author.id,
    name: author.name,
    username: author.username,
    bio: author.bio,
    avatar: author.avatar,
    roleTitle: author.roleTitle,
    location: author.location
  }) : null;

  useEffect(() => {
    const fetchAuthor = async () => {
      if (!cleanUsername) return;

      try {
        setIsLoading(true);
        setError(null);

        const response = await getAuthorByUsername(cleanUsername);

        if (response.success) {
          setAuthor(response.data);
          setStoriesPage(1);
          setBlogsPage(1);
          setCaseStudiesPage(1);
          setStories([]);
          setBlogs([]);
          setCaseStudies([]);
          setHasMoreStories(true);
          setHasMoreBlogs(true);
          setHasMoreCaseStudies(true);

          // Fetch startup profile
          try {
            const startupResponse = await getStartupProfileByAuthor(cleanUsername);
            if (startupResponse.success && startupResponse.data) {
              setStartupProfile(startupResponse.data);
            }
          } catch (startupErr) {
            console.error('Error fetching startup profile:', startupErr);
          }
        } else {
          setError('Author not found');
        }
      } catch (err) {
        console.error('Error fetching author:', err);
        setError('Author not found');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAuthor();
  }, [cleanUsername]);

  useEffect(() => {
    const fetchStories = async () => {
      if (!cleanUsername || !hasMoreStories) return;

      try {
        setIsStoriesLoading(true);

        const response = await getAuthorStories(cleanUsername, storiesPage);

        if (response.success) {
          setStories(prevStories => {
            // Deduplicate by combining arrays and filtering unique items by _id
            const combined = [...prevStories, ...response.data];
            const uniqueStories = combined.filter((story, index, self) =>
              index === self.findIndex(s => s._id === story._id)
            );
            return uniqueStories;
          });
          setHasMoreStories(response.pagination.hasMore);
        }
      } catch (err) {
        console.error('Error fetching stories:', err);
      } finally {
        setIsStoriesLoading(false);
      }
    };

    fetchStories();
  }, [cleanUsername, storiesPage]);

  useEffect(() => {
    const fetchBlogs = async () => {
      if (!cleanUsername || !hasMoreBlogs) return;

      try {
        setIsBlogsLoading(true);

        const response = await getAuthorBlogs(cleanUsername, blogsPage);

        if (response.success) {
          setBlogs(prevBlogs => {
            // Deduplicate by combining arrays and filtering unique items by _id
            const combined = [...prevBlogs, ...response.data];
            const uniqueBlogs = combined.filter((blog, index, self) =>
              index === self.findIndex(b => b._id === blog._id)
            );
            return uniqueBlogs;
          });
          setHasMoreBlogs(response.pagination.hasMore);
        }
      } catch (err) {
        console.error('Error fetching blogs:', err);
      } finally {
        setIsBlogsLoading(false);
      }
    };

    fetchBlogs();
  }, [cleanUsername, blogsPage]);

  useEffect(() => {
    const fetchCaseStudies = async () => {
      if (!cleanUsername || !hasMoreCaseStudies) return;

      try {
        setIsCaseStudiesLoading(true);

        const response = await getAuthorCaseStudies(cleanUsername, caseStudiesPage);

        if (response.success) {
          setCaseStudies(prevCaseStudies => {
            // Deduplicate by combining arrays and filtering unique items by _id
            const combined = [...prevCaseStudies, ...response.data];
            const uniqueCaseStudies = combined.filter((caseStudy, index, self) =>
              index === self.findIndex(cs => cs._id === caseStudy._id)
            );
            return uniqueCaseStudies;
          });
          setHasMoreCaseStudies(response.pagination.hasMore);
        }
      } catch (err) {
        console.error('Error fetching case studies:', err);
      } finally {
        setIsCaseStudiesLoading(false);
      }
    };

    fetchCaseStudies();
  }, [cleanUsername, caseStudiesPage]);

  // Handle like function
  const handleLike = async (e: React.MouseEvent, id: string, type: 'story' | 'blog' | 'caseStudy') => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      let response;
      if (type === 'story') {
        response = await toggleStoryLike(id);
      } else if (type === 'blog') {
        response = await toggleBlogLike(id);
      } else {
        response = await caseStudyService.likeCaseStudy(id);
      }

      if (response.success && response.data) {
        // Update the appropriate content list with new like data
        if (type === 'story') {
          setStories(prev => prev.map(story => {
            if (story._id === id) {
              return {
                ...story,
                likes: response.data.likes,
                likedBy: response.data.likedBy || []
              };
            }
            return story;
          }));
        } else if (type === 'blog') {
          setBlogs(prev => prev.map(blog => {
            if (blog._id === id) {
              return {
                ...blog,
                likes: response.data.likes,
                likedBy: response.data.likedBy || []
              };
            }
            return blog;
          }));
        } else {
          setCaseStudies(prev => prev.map(caseStudy => {
            if (caseStudy._id === id) {
              return {
                ...caseStudy,
                likes: response.data.likes,
                likedBy: response.data.likedBy || []
              };
            }
            return caseStudy;
          }));
        }

        toast({
          title: response.data.isLiked ? `${type} liked!` : 'Like removed',
          description: response.data.isLiked ? `Added to your liked ${type}s` : `Removed from your liked ${type}s`
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Check favorites status when user is authenticated and content is loaded
  useEffect(() => {
    const checkFavoritesStatus = async () => {
      if (isAuthenticated) {
        try {
          // Check stories favorites
          if (stories.length > 0) {
            const storyChecks = await Promise.all(
              stories.map(story => favoriteService.isFavorited('story', story._id))
            );
            const newStoryFavorites = new Set<string>();
            storyChecks.forEach((response, index) => {
              if (response?.success && response?.data?.isFavorited) {
                newStoryFavorites.add(stories[index]._id);
              }
            });
            setFavoritedStories(newStoryFavorites);
          }

          // Check blogs favorites
          if (blogs.length > 0) {
            const blogChecks = await Promise.all(
              blogs.map(blog => favoriteService.isFavorited('blog', blog._id))
            );
            const newBlogFavorites = new Set<string>();
            blogChecks.forEach((response, index) => {
              if (response?.success && response?.data?.isFavorited) {
                newBlogFavorites.add(blogs[index]._id);
              }
            });
            setFavoritedBlogs(newBlogFavorites);
          }

          // Check case studies favorites
          if (caseStudies.length > 0) {
            const caseStudyChecks = await Promise.all(
              caseStudies.map(caseStudy => favoriteService.isFavorited('caseStudy', caseStudy._id))
            );
            const newCaseStudyFavorites = new Set<string>();
            caseStudyChecks.forEach((response, index) => {
              if (response?.success && response?.data?.isFavorited) {
                newCaseStudyFavorites.add(caseStudies[index]._id);
              }
            });
            setFavoritedCaseStudies(newCaseStudyFavorites);
          }
        } catch (error) {
          console.error('Error checking favorites status:', error);
        }
      }
    };

    checkFavoritesStatus();
  }, [isAuthenticated, stories, blogs, caseStudies]);

  const loadMoreStories = () => {
    if (!isStoriesLoading && hasMoreStories) {
      setStoriesPage(prev => prev + 1);
    }
  };

  const loadMoreBlogs = () => {
    if (!isBlogsLoading && hasMoreBlogs) {
      setBlogsPage(prev => prev + 1);
    }
  };

  const loadMoreCaseStudies = () => {
    if (!isCaseStudiesLoading && hasMoreCaseStudies) {
      setCaseStudiesPage(prev => prev + 1);
    }
  };

  const openStory = (story: Story) => {
    const url = `/story/${story._id}`;
    navigate(url);
  };

  const openBlog = (blog: Blog) => {
    const url = `/blog/${blog.slug || blog._id}`;
    navigate(url);
  };

  const openCaseStudy = (caseStudy: CaseStudy) => {
    const url = `/case-study/${caseStudy.slug || caseStudy._id}`;
    navigate(url);
  };

  // Helper function to check if user has liked a content item
  const isLikedByUser = (item: Story | Blog | CaseStudy) => {
    if (!user || !item.likedBy) return false;
    return item.likedBy.includes(user.id);
  };

  // Toggle favorite function
  const toggleFavorite = async (e: React.MouseEvent, id: string, type: 'story' | 'blog' | 'caseStudy') => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await favoriteService.toggleFavorite(type, id);

      if (response.success && response.data) {
        if (type === 'story') {
          const newFavorites = new Set(favoritedStories);
          if (response.data.isFavorited) {
            newFavorites.add(id);
          } else {
            newFavorites.delete(id);
          }
          setFavoritedStories(newFavorites);
        } else if (type === 'blog') {
          const newFavorites = new Set(favoritedBlogs);
          if (response.data.isFavorited) {
            newFavorites.add(id);
          } else {
            newFavorites.delete(id);
          }
          setFavoritedBlogs(newFavorites);
        } else {
          const newFavorites = new Set(favoritedCaseStudies);
          if (response.data.isFavorited) {
            newFavorites.add(id);
          } else {
            newFavorites.delete(id);
          }
          setFavoritedCaseStudies(newFavorites);
        }

        toast({
          title: response.data.isFavorited ? "Added to favorites" : "Removed from favorites",
          description: response.data.isFavorited ? `${type} has been bookmarked` : `${type} has been removed from bookmarks`,
        });
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast({
        title: "Error",
        description: "Failed to update bookmark status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const renderContentCard = (item: Story | Blog | CaseStudy, type: 'story' | 'blog' | 'caseStudy', onClick: () => void) => {
    const isStory = type === 'story';
    const isBlog = type === 'blog';
    const isCaseStudy = type === 'caseStudy';
    const isLiked = isLikedByUser(item);
    const isFavorited = isStory ? favoritedStories.has(item._id) :
      isBlog ? favoritedBlogs.has(item._id) :
        favoritedCaseStudies.has(item._id);

    const getCategory = () => {
      if (isCaseStudy) return 'Case Study';
      if (isStory || isBlog) {
        const contentItem = item as Story | Blog;
        return typeof contentItem.category === 'object' ? contentItem.category.name : 'Uncategorized';
      }
      return 'Uncategorized';
    };

    const getReadTime = () => {
      if (isCaseStudy) return '5';
      const contentItem = item as Story | Blog;
      return Math.max(1, Math.ceil((contentItem.content?.split(/\s+/).length || 0) / 200)).toString();
    };

    const getExcerpt = () => {
      if (isCaseStudy) {
        const caseStudyItem = item as CaseStudy;
        return caseStudyItem.subheading || 'No description available';
      }
      const contentItem = item as Story | Blog;
      return contentItem.excerpt || 'No excerpt available';
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="cursor-pointer group h-full"
        onClick={onClick}
      >
        <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-card border-border hover:border-brand-200 dark:hover:border-brand-800 h-full flex flex-col">
          <div className="relative">
            <div className="w-full h-48 relative overflow-hidden">
              <img
                src={getFeaturedImageUrl(item.featuredImage)}
                alt={item.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default-story-cover.jpg';
                }}
              />
              {/* Action buttons overlay */}
              <div className="absolute top-3 right-3 flex gap-2">
                <Button
                  variant="secondary"
                  size="icon"
                  className={`h-8 w-8 bg-background/80 hover:bg-background ${isFavorited ? 'text-blue-500 dark:text-blue-400' : ''}`}
                  onClick={(e) => toggleFavorite(e, item._id, type)}
                  title={isAuthenticated ? (isFavorited ? `Remove from favorites` : `Add to favorites`) : `Login to favorite`}
                >
                  <Bookmark className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                </Button>
                <Button
                  variant="secondary"
                  size="icon"
                  className={`h-8 w-8 bg-background/80 hover:bg-background ${isLiked ? 'text-red-500 dark:text-red-400' : ''}`}
                  onClick={(e) => handleLike(e, item._id, type)}
                  title={isAuthenticated ? (isLiked ? `Unlike this ${type}` : `Like this ${type}`) : `Login to like`}
                >
                  <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                </Button>
              </div>

              {/* Premium badge */}
              {(item as any).isPremium && (
                <div className="absolute top-3 left-3">
                  <Badge className="bg-yellow-500 text-black hover:bg-yellow-600">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                </div>
              )}
            </div>
          </div>

          <CardContent className="p-6 flex-1 flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-brand-100 text-brand-800 hover:bg-brand-200 dark:bg-brand-900/20 dark:text-brand-300">
                  {getCategory()}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {getReadTime()} min read
                </span>
              </div>
              <span className="text-xs text-muted-foreground">
                {new Date(item.createdAt).toLocaleDateString()}
              </span>
            </div>

            <h3 className="text-xl font-bold mb-3 text-foreground group-hover:text-brand-600 transition-colors line-clamp-2 min-h-[3.5rem]">
              {item.title}
            </h3>

            <p className="text-muted-foreground mb-4 line-clamp-3 text-sm leading-relaxed flex-1 min-h-[4.5rem]">
              {getExcerpt()}
            </p>

            <div className="flex items-center justify-between pt-4 border-t border-border mt-auto">
              <div className="flex items-center text-sm text-muted-foreground space-x-4">
                <div className="flex items-center hover:text-foreground transition-colors">
                  <Eye size={16} className="mr-1" />
                  {item.views?.toLocaleString() || 0}
                </div>
                <button
                  onClick={(e) => handleLike(e, item._id, type)}
                  className={`flex items-center hover:text-red-500 transition-colors ${isLiked ? 'text-red-500' : ''
                    }`}
                  title={isAuthenticated ? (isLiked ? `Unlike this ${type}` : `Like this ${type}`) : `Login to like this ${type}`}
                >
                  <Heart size={16} className={`mr-1 ${isLiked ? 'fill-current' : ''}`} />
                  {item.likes?.toLocaleString() || 0}
                </button>
                {isStory && (
                  <div className="flex items-center hover:text-foreground transition-colors">
                    <MessageSquare size={16} className="mr-1" />
                    {((item as Story).comments?.length || 0)}
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="text-brand-600 hover:text-brand-700 hover:bg-brand-50 dark:hover:bg-brand-900/20"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick();
                }}
              >
                Read More
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (error) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <main className="container mx-auto px-4 py-16 flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto">
            <div className="mb-8">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-6">
                <UserX size={48} className="text-muted-foreground" />
              </div>
              <h1 className="text-3xl font-bold text-foreground mb-3">Author Not Found</h1>
              <p className="text-muted-foreground text-lg mb-2">
                We couldn't find the author profile you're looking for.
              </p>
              <p className="text-muted-foreground text-sm">
                The username might be incorrect or the author may have changed their profile.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={() => navigate(-1)}
                  className="bg-brand-600 hover:bg-brand-700 text-white"
                >
                  <span className="mr-2">←</span>
                  Go Back
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/')}
                  className="border-brand-200 text-brand-700 hover:bg-brand-50 dark:border-brand-800 dark:text-brand-300 dark:hover:bg-brand-900/20"
                >
                  <Home size={16} className="mr-2" />
                  Home
                </Button>
              </div>

              <div className="pt-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/directory')}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Search size={16} className="mr-2" />
                  Browse Verified Authors
                </Button>
              </div>
            </div>

            <div className="mt-12 pt-8 border-t border-border">
              <p className="text-sm text-muted-foreground mb-4">
                Looking for something specific?
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/stories')}
                  className="text-xs"
                >
                  Success Stories
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/blog')}
                  className="text-xs"
                >
                  Blog Posts
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/case-studies')}
                  className="text-xs"
                >
                  Case Studies
                </Button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (isLoading || !author) {
    return (
      <div className="min-h-screen bg-background flex flex-col">
        <Header />
        <main className="pt-8 pb-16 flex-1">
          <div className="container mx-auto px-4">
            {/* Author Profile Header Skeleton */}
            <div className="bg-card rounded-xl shadow-sm overflow-hidden border border-border">
              <div className="relative bg-gradient-to-r from-brand-600 to-purple-600 p-6">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-20 w-20 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-8 w-48 mb-2" />
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <div className="hidden sm:flex gap-2">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <Skeleton className="h-10 w-10 rounded-full" />
                  </div>
                </div>
                <div className="flex sm:hidden gap-2 mt-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <Skeleton className="h-10 w-10 rounded-full" />
                </div>
              </div>
              <div className="p-6">
                <Skeleton className="h-20 w-full mb-4" />
                <div className="flex flex-wrap gap-4">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-6 w-32" />
                </div>
              </div>
            </div>

            {/* Content Tabs Skeleton */}
            <div className="mt-8">
              <div className="bg-muted rounded-lg p-1 mb-6">
                <div className="grid grid-cols-3 gap-2">
                  <Skeleton className="h-10" />
                  <Skeleton className="h-10" />
                  <Skeleton className="h-10" />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="h-48 w-full" />
                    <CardContent className="p-6">
                      <Skeleton className="h-6 w-24 mb-3" />
                      <Skeleton className="h-8 w-full mb-2" />
                      <Skeleton className="h-16 w-full mb-4" />
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-8 w-8 rounded-full" />
                          <div>
                            <Skeleton className="h-4 w-24 mb-1" />
                            <Skeleton className="h-3 w-20" />
                          </div>
                        </div>
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* SEO Meta Tags */}
      {authorSEO && <SEO {...authorSEO} />}

      <Header />

      <main className="pt-8 pb-16 flex-1">
        <div className="container mx-auto px-4">
          {/* Author Profile Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card rounded-xl shadow-sm overflow-hidden border border-border"
          >
            {/* Gradient background section */}
            <div className="relative bg-gradient-to-r from-brand-600 to-purple-600 p-6">
              <div className="flex items-center gap-4">
                {/* Avatar */}
                <Avatar className="h-20 w-20 border-4 border-white/20 shadow-xl shrink-0">
                  <AvatarImage src={getImageUrl(author.avatar)} alt={author.name} />
                  <AvatarFallback className="text-2xl bg-brand-700 text-white">{author.name.charAt(0)}</AvatarFallback>
                </Avatar>

                {/* Author info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white truncate">{author.name}</h1>
                    {author.isVerified && (
                      <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="28" height="28" viewBox="0 0 48 48">
                        <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                      </svg>
                    )}
                  </div>
                  <p className="text-white/80 text-sm sm:text-base truncate">@{author.username}</p>
                  <p className="text-white/90 text-sm sm:text-base truncate">{author.roleTitle}</p>
                </div>

                {/* Social links */}
                <div className="hidden sm:flex gap-2">
                  {author.twitterUrl && (
                    <a
                      href={author.twitterUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                      </svg>
                    </a>
                  )}
                  {author.linkedinUrl && (
                    <a
                      href={author.linkedinUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                    >
                      <Linkedin size={18} />
                    </a>
                  )}
                  {author.websiteUrl && (
                    <a
                      href={author.websiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                    >
                      <Globe size={18} />
                    </a>
                  )}
                </div>
              </div>

              {/* Mobile social links */}
              <div className="flex sm:hidden gap-2 mt-4 justify-start">
                {author.twitterUrl && (
                  <a
                    href={author.twitterUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                    </svg>
                  </a>
                )}
                {author.linkedinUrl && (
                  <a
                    href={author.linkedinUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                  >
                    <Linkedin size={18} />
                  </a>
                )}
                {author.websiteUrl && (
                  <a
                    href={author.websiteUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors"
                  >
                    <Globe size={18} />
                  </a>
                )}
              </div>
            </div>

            {/* Bio and metadata section */}
            <div className="p-6 bg-card border-t border-border">
              <p className="text-muted-foreground text-base leading-relaxed">
                {author.bio}
              </p>

              <div className="mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Calendar size={16} className="mr-2 text-brand-600" />
                  Joined {new Date(author.createdAt).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                </div>
                {author.location && (
                  <div className="flex items-center">
                    <MapPin size={16} className="mr-2 text-brand-600" />
                    {author.location}
                  </div>
                )}
                {author.availability && (
                  <div className="flex items-center">
                    <Mail size={16} className="mr-2 text-brand-600" />
                    {author.availability}
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Startup Profile Section */}
          {startupProfile && (!startupProfile.status || startupProfile.status === 'approved') && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mt-8 bg-card rounded-xl shadow-sm overflow-hidden border border-border"
            >
              <div className="p-6">
                <div className="flex items-center mb-6">
                  {startupProfile.logoUrl && (
                    <div className="mr-4">
                      <img
                        src={getImageUrl(startupProfile.logoUrl)}
                        alt={startupProfile.startupName}
                        className="h-16 w-16 object-contain"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/images/default-logo.png';
                        }}
                      />
                    </div>
                  )}
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">{startupProfile.startupName}</h2>
                    <p className="text-brand-600 dark:text-brand-400">{startupProfile.tagline}</p>
                  </div>
                </div>

                <div className="my-4">
                  <p className="text-muted-foreground">{startupProfile.shortDescription}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <div className="flex items-center">
                    <Rocket size={22} className="text-brand-600 dark:text-brand-400 mr-2" />
                    <span className="text-muted-foreground mr-2">Founded:</span>
                    <span className="text-foreground">{startupProfile.foundedYear}</span>
                  </div>

                  <div className="flex items-center">
                    <HandCoins  size={22} className="text-brand-600 dark:text-brand-400 mr-2" />
                    <span className="text-muted-foreground mr-2">Funding Stage:</span>
                    <span className="text-foreground">{startupProfile.fundingStage}</span>
                  </div>

                  <div className="flex items-center">
                    <Award size={22} className="text-brand-600 dark:text-brand-400 mr-2" />
                    <span className="text-muted-foreground mr-2">Industry:</span>
                    <span className="text-foreground">{startupProfile.industry}</span>
                  </div>

                  <div className="flex items-center">
                    <DollarSign size={22} className="text-brand-600 dark:text-brand-400 mr-2" />
                    <span className="text-muted-foreground mr-2">Revenue Model:</span>
                    <span className="text-foreground">{startupProfile.revenueModel}</span>
                  </div>
                </div>

                {startupProfile.keyMilestones && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2 text-foreground">Key Milestones</h3>
                    <p className="text-muted-foreground">{startupProfile.keyMilestones}</p>
                  </div>
                )}

                <div className="flex flex-wrap gap-4 mt-6">
                  {startupProfile.ctaButtonLink && (
                    <a
                      href={startupProfile.ctaButtonLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-brand-600 hover:bg-brand-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
                    >
                      {startupProfile.ctaButtonText || "Learn More"}
                    </a>
                  )}
                  {startupProfile.websiteUrl && (
                    <a
                      href={startupProfile.websiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-muted hover:bg-muted/80 text-foreground px-4 py-2 rounded-lg flex items-center transition-colors"
                    >
                      <Globe size={16} className="mr-2" />
                      Website
                    </a>
                  )}

                  {startupProfile.appStoreLink && (
                    <a
                      href={startupProfile.appStoreLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-muted hover:bg-muted/80 text-foreground px-4 py-2 rounded-lg flex items-center transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-5 mr-2 h-5 fill-current">
                        <path d="M255.9 120.9l9.1-15.7c5.6-9.8 18.1-13.1 27.9-7.5 9.8 5.6 13.1 18.1 7.5 27.9l-87.5 151.5h63.3c20.5 0 32 24.1 23.1 40.8H113.8c-11.3 0-20.4-9.1-20.4-20.4 0-11.3 9.1-20.4 20.4-20.4h52l66.6-115.4-20.8-36.1c-5.6-9.8-2.3-22.2 7.5-27.9 9.8-5.6 22.2-2.3 27.9 7.5l8.9 15.7zm-78.7 218l-19.6 34c-5.6 9.8-18.1 13.1-27.9 7.5-9.8-5.6-13.1-18.1-7.5-27.9l14.6-25.2c16.4-5.1 29.8-1.2 40.4 11.6zm168.9-61.7h53.1c11.3 0 20.4 9.1 20.4 20.4 0 11.3-9.1 20.4-20.4 20.4h-29.5l19.9 34.5c5.6 9.8 2.3 22.2-7.5 27.9-9.8 5.6-22.2 2.3-27.9-7.5-33.5-58.1-58.7-101.6-75.4-130.6-17.1-29.5-4.9-59.1 7.2-69.1 13.4 23 33.4 57.7 60.1 104zM256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm216 248c0 118.7-96.1 216-216 216-118.7 0-216-96.1-216-216 0-118.7 96.1-216 216-216 118.7 0 216 96.1 216 216z" />
                      </svg>
                      App Store
                    </a>
                  )}

                  {startupProfile.playStoreLink && (
                    <a
                      href={startupProfile.playStoreLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-muted hover:bg-muted/80 text-foreground px-4 py-2 rounded-lg flex items-center transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" className="w-4  mr-2 h-5 fill-current">
                        <path d="M325.3 234.3L104.6 13l280.8 161.2-60.1 60.1zM47 0C34 6.8 25.3 19.2 25.3 35.3v441.3c0 16.1 8.7 28.5 21.7 35.3l256.6-256L47 0zm425.2 225.6l-58.9-34.1-65.7 64.5 65.7 64.5 60.1-34.1c18-14.3 18-46.5-1.2-60.8zM104.6 499l280.8-161.2-60.1-60.1L104.6 499z" />
                      </svg>
                      Play Store
                    </a>
                  )}

                </div>
              </div>
            </motion.div>
          )}

          {/* Content Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-8"
          >
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-muted">
                <TabsTrigger value="stories" className="flex items-center gap-2">
                  <BookOpen size={16} />
                  Stories ({stories.length})
                </TabsTrigger>
                <TabsTrigger value="blogs" className="flex items-center gap-2">
                  <PenTool size={16} />
                  Blogs ({blogs.length})
                </TabsTrigger>
                <TabsTrigger value="case-studies" className="flex items-center gap-2">
                  <FileText size={16} />
                  Case Studies ({caseStudies.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="stories" className="mt-6">
                {stories.length === 0 && !isStoriesLoading ? (
                  <div className="bg-card rounded-xl shadow-sm p-8 text-center border border-border">
                    <BookOpen size={48} className="mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No stories found for this author.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {stories.map((story) => (
                      <div key={story._id}>
                        {renderContentCard(story, 'story', () => openStory(story))}
                      </div>
                    ))}

                    {isStoriesLoading && (
                      Array.from({ length: 6 }).map((_, index) => (
                        <Card key={index} className="overflow-hidden">
                          <Skeleton className="w-full h-48" />
                          <CardContent className="p-6">
                            <Skeleton className="h-6 w-24 mb-3" />
                            <Skeleton className="h-8 w-full mb-2" />
                            <Skeleton className="h-16 w-full mb-4" />
                            <Skeleton className="h-4 w-48" />
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                )}

                {hasMoreStories && !isStoriesLoading && stories.length > 0 && (
                  <div className="text-center mt-8">
                    <Button onClick={loadMoreStories} variant="outline">
                      Load More Stories
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="blogs" className="mt-6">
                {blogs.length === 0 && !isBlogsLoading ? (
                  <div className="bg-card rounded-xl shadow-sm p-8 text-center border border-border">
                    <PenTool size={48} className="mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No blogs found for this author.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {blogs.map((blog) => (
                      <div key={blog._id}>
                        {renderContentCard(blog, 'blog', () => openBlog(blog))}
                      </div>
                    ))}

                    {isBlogsLoading && (
                      Array.from({ length: 6 }).map((_, index) => (
                        <Card key={index} className="overflow-hidden">
                          <Skeleton className="aspect-video w-full" />
                          <CardContent className="p-6">
                            <Skeleton className="h-6 w-24 mb-3" />
                            <Skeleton className="h-8 w-full mb-2" />
                            <Skeleton className="h-16 w-full mb-4" />
                            <Skeleton className="h-4 w-48" />
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                )}

                {hasMoreBlogs && !isBlogsLoading && blogs.length > 0 && (
                  <div className="text-center mt-8">
                    <Button onClick={loadMoreBlogs} variant="outline">
                      Load More Blogs
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="case-studies" className="mt-6">
                {caseStudies.length === 0 && !isCaseStudiesLoading ? (
                  <div className="bg-card rounded-xl shadow-sm p-8 text-center border border-border">
                    <FileText size={48} className="mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No case studies found for this author.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {caseStudies.map((caseStudy) => (
                      <div key={caseStudy._id}>
                        {renderContentCard(caseStudy, 'caseStudy', () => openCaseStudy(caseStudy))}
                      </div>
                    ))}

                    {isCaseStudiesLoading && (
                      Array.from({ length: 6 }).map((_, index) => (
                        <Card key={index} className="overflow-hidden">
                          <Skeleton className="w-full h-48" />
                          <CardContent className="p-6">
                            <Skeleton className="h-6 w-24 mb-3" />
                            <Skeleton className="h-8 w-full mb-2" />
                            <Skeleton className="h-16 w-full mb-4" />
                            <Skeleton className="h-4 w-48" />
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                )}

                {hasMoreCaseStudies && !isCaseStudiesLoading && caseStudies.length > 0 && (
                  <div className="text-center mt-8">
                    <Button onClick={loadMoreCaseStudies} variant="outline">
                      Load More Case Studies
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </main>

      <Footer />

      {/* Login Dialog */}
      <UserLogin
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
      />
    </div>
  );
};

export default AuthorProfilePage;
