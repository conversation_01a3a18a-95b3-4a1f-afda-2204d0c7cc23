
import React from 'react';
import { Star } from 'lucide-react';

const testimonials = [
  {
    quote: "The founder stories felt so real and relatable. I picked up practical growth tactics from case studies that I actually used to launch my SaaS app! The community feedback made all the difference.",
    author: "<PERSON>",
    role: "CEO at TechSolutions",
    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    rating: 5
  },
  {
    quote: "The premium case studies are gold. Seeing real revenue numbers and marketing strategies let me skip so much guesswork. I landed my first paying customers using what I learned here.",
    author: "<PERSON>",
    role: "Founder, BlockChain Ventures",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    rating: 5
  },
  {
    quote: "Getting listed in the entrepreneur directory and connecting with others through comments helped me find my first beta testers. It's more than a resource, it's a real community.",
    author: "Aisha Patel",
    role: "Co-founder, AI Solutions",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    rating: 5
  },
];

const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          size={16}
          className={i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
        />
      ))}
    </div>
  );
};

const TestimonialsSection = () => {
  return (
    <section className="py-16 gradient-bg text-white">
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Real Stories. Real Impact.</h2>
          <p className="text-lg opacity-90">
          See how founders from around the world are turning their ideas into reality with support from our platform.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index} 
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/20"
            >
              <div className="mb-4">
                <StarRating rating={testimonial.rating} />
              </div>
              <p className="mb-6 italic">"{testimonial.quote}"</p>
              <div className="flex items-center">
                <img 
                  src={testimonial.avatar}
                  alt={testimonial.author}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="ml-3">
                  <p className="font-medium">{testimonial.author}</p>
                  <p className="text-sm opacity-80">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <p className="text-xl font-medium mb-8">Join over 5,000+ entrepreneurs who've found value in our community</p>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;

