import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ChevronUp, Search, Grid, List, Layout, Bookmark, BookOpen, ChevronLeft, ChevronRight, Heart, Eye, Clock, User, Calendar, Crown } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import UserLogin from "@/components/UserLogin";
import SEO from "@/components/SEO";
import { getPublishedBlogs, getBlogsByCategory, getBlogCategories, toggleBlogLike } from '@/services/blogService';
import favoriteService from '@/services/favoriteService';
import { Blog, Author, BlogCategory } from '@/types';
import { useAuth } from '@/lib/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { formatDate } from '@/lib/utils';

const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const BlogPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'magazine'>('grid');
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    total: 0
  });
  const [hasMore, setHasMore] = useState(true);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [favoritedBlogs, setFavoritedBlogs] = useState<Set<string>>(new Set());
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { toast } = useToast();

  // Mouse drag scrolling state
  const categoryScrollRef = React.useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Helper function to determine if a blog is liked by the current user
  const isLikedByUser = (blog: Blog): boolean => {
    // Don't show as liked if we don't have authenticated user data
    if (!isAuthenticated || !user?.id || !blog.likedBy) {
      return false;
    }

    // Check if the current user's ID is in the likedBy array
    return blog.likedBy.some(likedUserId => {
      if (!likedUserId) return false;
      // Convert both to strings for comparison to handle ObjectId/string differences
      return likedUserId.toString() === user.id.toString();
    });
  };

  // Load initial data
  useEffect(() => {
    loadCategories();
    loadBlogs(1);
  }, []);

  // Check favorites status when user is authenticated and blogs are loaded
  useEffect(() => {
    const checkFavoritesStatus = async () => {
      if (isAuthenticated && blogs.length > 0) {
        try {
          const favoriteChecks = await Promise.all(
            blogs.map(blog => favoriteService.isFavorited('blog', blog._id))
          );

          const newFavorites = new Set<string>();
          favoriteChecks.forEach((response, index) => {
            if (response.success && response.data?.isFavorited && blogs[index]) {
              newFavorites.add(blogs[index]._id);
            }
          });

          setFavoritedBlogs(newFavorites);
        } catch (error) {
          console.error('Error checking favorites status:', error);
        }
      }
    };

    checkFavoritesStatus();
  }, [isAuthenticated, blogs]);

  // Load categories
  const loadCategories = async () => {
    try {
      const response = await getBlogCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  // Load blogs function
  const loadBlogs = async (page = 1, reset = true, categoryId = selectedCategory) => {
    try {
      setIsLoading(true);
      let response;

      if (categoryId === 'all') {
        response = await getPublishedBlogs(page, 12);
      } else {
        response = await getBlogsByCategory(categoryId, page, 12);
      }

      if (response.success) {
        if (reset) {
          setBlogs(response.data);
        } else {
          setBlogs(prev => [...prev, ...response.data]);
        }

        setPagination(response.pagination);
        setHasMore(page < response.pagination.pages);
      }
    } catch (error) {
      console.error('Error loading blogs:', error);
      toast({
        title: "Error",
        description: "Failed to load blogs. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle category change
  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    loadBlogs(1, true, categoryId);
  };

  // Load more blogs
  const loadMoreBlogs = () => {
    if (hasMore && !isLoading) {
      loadBlogs(pagination.page + 1, false);
    }
  };

  // Show button when page is scrolled down
  React.useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Mouse drag scrolling handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!categoryScrollRef.current) return;

    setIsDragging(true);
    setStartX(e.pageX - categoryScrollRef.current.offsetLeft);
    setScrollLeft(categoryScrollRef.current.scrollLeft);

    // Prevent text selection
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !categoryScrollRef.current) return;

    e.preventDefault();
    const x = e.pageX - categoryScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Multiply by 2 for faster scrolling
    categoryScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Filter blogs based on search term
  const filteredBlogs = blogs.filter(blog =>
    blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    blog.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (typeof blog.category === 'object' && blog.category.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    blog.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Toggle like
  const toggleLike = async (e: React.MouseEvent, blogId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await toggleBlogLike(blogId);

      if (response.success) {
        // Update local state
        setBlogs(prev => prev.map(b => {
          if (b._id === blogId) {
            return {
              ...b,
              likes: response.data.likes,
              likedBy: response.data.likedBy
            };
          }
          return b;
        }));

        toast({
          title: response.data.isLiked ? "Blog liked" : "Blog unliked",
          description: response.data.isLiked ? "Added to your liked blogs" : "Removed from your liked blogs",
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Toggle favorite
  const toggleFavorite = async (e: React.MouseEvent, blogId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    try {
      const response = await favoriteService.toggleFavorite('blog', blogId);

      if (response.success) {
        const newFavorites = new Set(favoritedBlogs);
        if (response.data.isFavorited) {
          newFavorites.add(blogId);
        } else {
          newFavorites.delete(blogId);
        }
        setFavoritedBlogs(newFavorites);

        toast({
          title: response.data.isFavorited ? "Blog bookmarked" : "Bookmark removed",
          description: response.data.isFavorited ? "Added to your favorites" : "Removed from your favorites",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update bookmark status. Please try again.",
        variant: "destructive",
      });
    }
  };


  // Get author info
  const getAuthorInfo = (author: string | Author) => {
    if (typeof author === 'string') {
      return { name: 'Anonymous', avatar: getAvatarUrl('/default-avatar.png'), isVerified: false, roleTitle: 'Writer', username: 'anonymous' };
    }
    return {
      name: author.name,
      avatar: getAvatarUrl(author.avatar || '/default-avatar.png'),
      isVerified: author.isVerified,
      roleTitle: author.roleTitle,
      username: author.username || author.name?.toLowerCase().replace(/\s+/g, '') || 'unknown'
    };
  };

  const openAuthorProfile = (e: React.MouseEvent, author: string | Author) => {
    e.preventDefault();
    e.stopPropagation();
    const authorInfo = getAuthorInfo(author);
    const url = `/author/@${authorInfo.username}`;
    window.open(url, '_self');
  };

  // Get featured image URL
  const getFeaturedImageUrl = (blog: Blog) => {
    if (blog.featuredImage?.path) {
      return blog.featuredImage.path.startsWith('uploads')
        ? BACKEND_URL + blog.featuredImage.path
        : blog.featuredImage.path;
    }
    return 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485'; // fallback image
  };

  // Grid View
  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {filteredBlogs.map((blog) => {
        const authorInfo = getAuthorInfo(blog.author);
        // Use the helper function to determine if liked
        const isLiked = isLikedByUser(blog);

        return (
          <Link to={`/blog/${blog.slug || blog._id}`} key={blog._id} className="group">
            <div className="bg-card rounded-lg overflow-hidden shadow-md transition-shadow hover:shadow-lg h-full flex flex-col border border-border">
              <div className="w-full h-56 relative flex-shrink-0 overflow-hidden">
                <img
                  src={getFeaturedImageUrl(blog)}
                  alt={blog.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-3 left-3 flex flex-col items-start gap-1">
                  <Badge >
                    {typeof blog.category === 'object' ? blog.category.name : blog.category}
                  </Badge>
                  {blog.isPremium && (
                    <Badge className="bg-yellow-500 text-black hover:bg-yellow-600">
                      <Crown className="h-3 w-3 mr-1" />
                      Premium
                    </Badge>
                  )}
                </div>
                <div className="absolute top-3 right-3 flex space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className={`h-8 w-8 bg-background/80 hover:bg-background ${favoritedBlogs.has(blog._id) ? 'text-blue-500' : ''}`}
                    onClick={(e) => toggleFavorite(e, blog._id)}
                  >
                    <Bookmark className={`h-4 w-4 ${favoritedBlogs.has(blog._id) ? 'fill-current' : ''}`} />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    className={`h-8 w-8 bg-background/80 hover:bg-background ${isLiked ? 'text-red-500' : ''}`}
                    onClick={(e) => toggleLike(e, blog._id)}
                  >
                    <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                  </Button>
                </div>
              </div>
              <div className="p-5 flex-grow flex flex-col">
                <h3 className="text-xl font-bold mb-2 group-hover:text-brand-600 transition-colors text-foreground h-14 overflow-hidden line-clamp-2">
                  {blog.title}
                </h3>
                <p className="text-muted-foreground mb-4 line-clamp-2 flex-grow h-12 overflow-hidden">
                  {blog.excerpt}
                </p>
                <div className="flex items-center justify-between mt-auto">
                  <div
                    className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={(e) => openAuthorProfile(e, blog.author)}
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden">
                      <img
                        src={authorInfo.avatar}
                        alt={authorInfo.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-foreground hover:text-brand-600 transition-colors">{authorInfo.name}</span>
                        {authorInfo.isVerified && (
                          <div className="ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {authorInfo.roleTitle || 'Writer'}
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(blog.publishDate)} · {blog.readTime} min read
                  </div>
                </div>
              </div>
              <div className="bg-muted/50 px-5 py-2 text-xs text-muted-foreground flex justify-between">
                <span className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <Heart className="h-3 w-3" />
                    <span>{blog.likes || 0}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>{blog.views || 0}</span>
                  </span>
                </span>
              </div>
            </div>
          </Link>
        );
      })}
    </div>
  );

  // List View
  const ListView = () => (
    <div className="space-y-4">
      {filteredBlogs.map((blog) => {
        const authorInfo = getAuthorInfo(blog.author);
        // Use the helper function to determine if liked
        const isLiked = isLikedByUser(blog);

        return (
          <div key={blog._id} className="bg-card rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-border">
            <div className="flex flex-col md:flex-row">
              <div className="md:w-1/3 lg:w-1/4">
                <Link to={`/blog/${blog.slug || blog._id}`}>
                  <div className="relative">
                    <div className="aspect-[4/3] w-full overflow-hidden rounded-lg">
                      <img
                        src={getFeaturedImageUrl(blog)}
                        alt={blog.title}
                        className="w-full h-full object-cover transition-transform hover:scale-105"
                      />
                    </div>
                    <div className="absolute top-3 left-3 flex flex-col items-start gap-1">
                      <Badge >
                        {typeof blog.category === 'object' ? blog.category.name : blog.category}
                      </Badge>
                      {blog.isPremium && (
                        <Badge className="bg-yellow-500 text-black hover:bg-yellow-600">
                          <Crown className="h-3 w-3 mr-1" />
                          Premium
                        </Badge>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
              <div className="p-5 md:w-2/3 lg:w-3/4 flex flex-col">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-xs text-muted-foreground">
                    {blog.readTime} min read
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(blog.publishDate)}
                  </span>
                </div>
                <Link to={`/blog/${blog.slug || blog._id}`}>
                  <h3 className="font-bold text-xl mb-2 hover:text-primary transition-colors text-foreground">
                    {blog.title}
                  </h3>
                </Link>
                <p className="text-muted-foreground mb-4 flex-grow">
                  {blog.excerpt}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1" />
                      {blog.views || 0}
                    </div>
                    <div className="flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      {blog.likes || 0}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 ${favoritedBlogs.has(blog._id) ? 'text-blue-500' : ''}`}
                      onClick={(e) => toggleFavorite(e, blog._id)}
                    >
                      <Bookmark className={`h-4 w-4 ${favoritedBlogs.has(blog._id) ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-8 w-8 ${isLiked ? 'text-red-500' : ''}`}
                      onClick={(e) => toggleLike(e, blog._id)}
                    >
                      <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                    </Button>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-auto">
                  <Link to={`/author/@${authorInfo.username}`} className="flex items-center group">
                    <img
                      src={authorInfo.avatar}
                      alt={authorInfo.name}
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    <div>
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                          {authorInfo.name}
                        </p>
                        {authorInfo.isVerified && (
                          <div className="ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {authorInfo.roleTitle || 'Writer'}
                      </p>
                    </div>
                  </Link>

                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/blog/${blog.slug || blog._id}`}>
                      Read Blog <ChevronRight size={16} className="ml-1" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );


  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="py-16">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-3 text-foreground">Blog</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Insights, trends, and knowledge from our community of entrepreneurs and industry experts.
            </p>
          </div>

          {/* Category Slider */}
          <div className="mt-8 mb-6 w-full -mx-4">
            <div className="w-full overflow-hidden">
              <div
                ref={categoryScrollRef}
                className={`flex items-center space-x-3 overflow-x-auto scrollbar-hide pb-2 w-full px-4
                          scroll-smooth touch-pan-x select-none ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none',
                  WebkitOverflowScrolling: 'touch'
                }}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}>
                <Button
                  variant={selectedCategory === 'all' ? 'default' : 'outline'}
                  className={`whitespace-nowrap shrink-0 min-w-fit ${selectedCategory === 'all' ? 'bg-brand-600 hover:bg-brand-700' : ''}`}
                  onClick={() => handleCategoryChange('all')}
                >
                  All Categories
                </Button>
                {categories?.map((category) => (
                  <Button
                    key={category._id}
                    variant={selectedCategory === category._id ? 'default' : 'outline'}
                    className={`whitespace-nowrap shrink-0 min-w-fit ${selectedCategory === category._id ? 'bg-brand-600 hover:bg-brand-700' : ''}`}
                    onClick={() => handleCategoryChange(category._id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
          </div>
          {/* Search and View Mode */}
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-8 mb-8">
            <div className="relative w-full md:max-w-md">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <Input
                placeholder="Search articles..."
                className="pl-10 bg-background border-border"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="hidden md:flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">View:</span>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <Grid size={18} />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List size={18} />
              </Button>
            </div>
          </div>

          {/* Blog Content */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="w-full h-48 bg-gray-200 animate-pulse"></div>
                  <CardContent className="p-5">
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="h-4 w-28 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredBlogs.length === 0 ? (
            <div className="text-center py-16">
              <p className="text-gray-500">No articles found matching your search.</p>
              <Button variant="link" onClick={() => setSearchTerm('')}>
                Clear search
              </Button>
            </div>
          ) : (
            <>
              {viewMode === 'grid' && <GridView />}
              {viewMode === 'list' && <ListView />}
            </>
          )}

          {/* Load More Button */}
          {hasMore && !isLoading && filteredBlogs.length > 0 && (
            <div className="text-center mt-12">
              <Button
                onClick={loadMoreBlogs}
                variant="outline"
                size="lg"
                className="px-8"
              >
                <BookOpen className="mr-2 h-4 w-4" />
                Load More Articles
              </Button>
            </div>
          )}
        </div>
      </main>

      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}

      <Footer />

      {/* Login Dialog */}
      <UserLogin
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
      />
    </div>
  );
};

export default BlogPage;
